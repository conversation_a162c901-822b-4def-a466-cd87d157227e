<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统 - 参数曲线</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入 SheetJS 用于Excel导出 -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 引入配置文件 -->
    <script src="config.js"></script>
    <style>
        /* 参数曲线页面专用样式 */
        .parameter-curve-container {
            width: 1920px;
            height: 932px;
            margin: 0 auto;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            overflow: hidden;
            position: relative;
            font-family: var(--font-family);
        }

        /* 科技感背景动画 */
        .parameter-curve-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 顶部标题栏 */
        .parameter-header {
            height: 80px;
            background: linear-gradient(90deg,
                rgba(26, 31, 46, 0.95) 0%,
                rgba(42, 49, 66, 0.95) 50%,
                rgba(26, 31, 46, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 10;
        }

        .parameter-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .parameter-title i {
            color: var(--accent-color);
            font-size: 32px;
        }

        /* 主内容区域 */
        .parameter-main-content {
            height: calc(100% - 80px);
            display: flex;
            padding: 25px;
            gap: 25px;
        }

        /* 左侧控制面板 */
        .parameter-control-panel {
            width: 420px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 25px;
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        /* 时间筛选区域 */
        .time-filter-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: var(--accent-color);
        }

        .time-mode-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .time-mode-btn {
            flex: 1;
            padding: 8px 12px;
            background: rgba(42, 49, 66, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 6px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            text-align: center;
        }

        .time-mode-btn:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: var(--accent-color);
        }

        .time-mode-btn.active {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--accent-color);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        /* 历史模式时间控件样式 */
        .history-time-controls {
            margin-top: 15px;
            padding: 15px;
            background: rgba(42, 49, 66, 0.4);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 8px;
        }

        .time-shortcuts {
            margin-bottom: 15px;
        }

        .shortcuts-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .shortcuts-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6px;
        }

        .shortcut-btn {
            padding: 6px 8px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 4px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 11px;
            text-align: center;
        }

        .shortcut-btn:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: var(--accent-color);
        }

        .shortcut-btn.active {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--accent-color);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .custom-time-selection {
            margin-bottom: 15px;
        }

        .time-input-group {
            margin-bottom: 10px;
        }

        .time-input-group label {
            display: block;
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .time-input {
            width: 100%;
            padding: 6px 8px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            color: var(--text-primary);
            font-size: 12px;
        }

        .time-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .time-apply-section {
            text-align: center;
        }

        .apply-time-btn {
            padding: 8px 16px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            border-radius: 6px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
            margin: 0 auto;
        }

        .apply-time-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        }

        .apply-time-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* 导出功能相关样式 */
        .echarts-toolbox {
            background: rgba(26, 31, 46, 0.8) !important;
            border: 1px solid rgba(0, 212, 255, 0.3) !important;
            border-radius: 6px !important;
        }

        /* 数据视图弹窗样式优化 */
        .echarts-data-view {
            background: rgba(26, 31, 46, 0.95) !important;
            border: 1px solid rgba(0, 212, 255, 0.3) !important;
            border-radius: 8px !important;
        }

        .echarts-data-view table {
            color: #ffffff !important;
        }

        .echarts-data-view th {
            background: rgba(0, 212, 255, 0.2) !important;
            color: #ffffff !important;
        }

        .echarts-data-view td {
            color: #ffffff !important;
        }

        .history-time-selector {
            display: none;
        }

        .history-time-selector.show {
            display: block;
        }

        .history-time-select {
            width: 100%;
            padding: 8px 12px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 6px;
            color: var(--text-primary);
            font-size: 12px;
        }

        .history-time-select:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        /* 曲线类型选择区域 */
        .curve-type-section {
            margin-bottom: 25px;
        }

        .curve-type-select {
            width: 100%;
            padding: 10px 12px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 14px;
            cursor: pointer;
        }

        .curve-type-select:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        .curve-type-select option {
            background: var(--bg-primary);
            color: var(--text-primary);
            padding: 8px;
        }

        /* 参数控制区域 */
        .parameter-control-section {
            margin-bottom: 25px;
        }

        /* 下拉多选框样式 */
        .parameter-multiselect-container {
            position: relative;
            width: 100%;
        }

        .parameter-multiselect {
            width: 100%;
            min-height: 42px;
            padding: 8px 12px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 14px;
            cursor: pointer;
            position: relative;
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            align-items: center;
        }

        .parameter-multiselect:hover {
            border-color: var(--accent-color);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        .parameter-multiselect-placeholder {
            color: var(--text-secondary);
            font-size: 13px;
        }

        .parameter-tag {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.4);
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 11px;
            color: var(--text-primary);
            display: inline-flex;
            align-items: center;
            gap: 4px;
            max-width: 120px;
        }

        .parameter-tag-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .parameter-tag-remove {
            cursor: pointer;
            font-size: 10px;
            color: var(--text-secondary);
        }

        .parameter-tag-remove:hover {
            color: var(--accent-color);
        }

        .parameter-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: rgba(26, 31, 46, 0.95);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            backdrop-filter: blur(10px);
            margin-top: 4px;
        }

        .parameter-dropdown.show {
            display: block;
        }

        .parameter-dropdown-item {
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            border-bottom: 1px solid rgba(0, 212, 255, 0.1);
            font-size: 13px;
        }

        .parameter-dropdown-item:last-child {
            border-bottom: none;
        }

        .parameter-dropdown-item:hover {
            background: rgba(0, 212, 255, 0.1);
        }

        .parameter-dropdown-item.selected {
            background: rgba(0, 212, 255, 0.2);
        }

        .parameter-checkbox {
            width: 16px;
            height: 16px;
            accent-color: var(--accent-color);
        }

        .parameter-dropdown-color {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .parameter-dropdown-text {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .parameter-dropdown-identifier {
            font-size: 11px;
            color: var(--text-muted);
            font-family: monospace;
        }

        /* 右侧图表区域 */
        .parameter-chart-area {
            flex: 1;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 25px;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .chart-control-btn {
            padding: 8px 16px;
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid var(--accent-color);
            border-radius: 6px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .chart-control-btn:hover {
            background: rgba(0, 212, 255, 0.3);
            transform: translateY(-2px);
        }

        .chart-control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .chart-control-btn.active {
            background: rgba(0, 212, 255, 0.4);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        .chart-container {
            flex: 1;
            min-height: 650px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

        /* API集成版本新增样式 */
        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: var(--text-muted);
            font-style: italic;
        }

        .no-parameters, .no-device {
            text-align: center;
            padding: 30px 20px;
            color: var(--text-muted);
            font-style: italic;
        }

        .parameter-control-item {
            background: rgba(42, 49, 66, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
            min-height: 50px;
            width: 100%;
            box-sizing: border-box;
            /* 防止长文本导致布局变形 */
            overflow: hidden;
        }

        .parameter-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .parameter-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            flex-shrink: 0;
        }

        .parameter-details {
            flex: 1;
            min-width: 0; /* 允许flex子项收缩 */
            overflow: hidden; /* 防止内容溢出 */
        }

        .parameter-name {
            font-size: 13px;
            color: var(--text-primary);
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%; /* 确保在父容器内 */
            cursor: help; /* 提示用户可以悬停查看完整内容 */
        }

        .parameter-identifier {
            font-size: 11px;
            color: var(--text-muted);
            font-family: monospace;
        }

        /* 开关样式 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 20px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 20px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        .toggle-switch input:checked + .toggle-slider {
            background-color: var(--primary-color);
        }

        .toggle-switch input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }
    </style>
</head>
<body>
    <div class="parameter-curve-container">
        <!-- 顶部标题栏 -->
        <header class="parameter-header">
            <h1 class="parameter-title">
                <i class="fas fa-chart-area"></i>
                参数曲线监控
            </h1>
        </header>

        <!-- 主内容区域 -->
        <main class="parameter-main-content">
            <!-- 左侧控制面板 -->
            <aside class="parameter-control-panel">
                <!-- 设备选择 -->
                <div class="curve-type-section">
                    <h3 class="section-title">
                        <i class="fas fa-list"></i>
                        设备选择
                    </h3>
                    <select class="curve-type-select" id="curveTypeSelect">
                        <option value="">请选择设备</option>
                        <!-- 设备选项将通过JavaScript动态生成 -->
                    </select>
                </div>

                <!-- 参数控制 -->
                <div class="parameter-control-section">
                    <h3 class="section-title">
                        <i class="fas fa-sliders-h"></i>
                        参数控制
                        <span class="parameter-count" id="parameterCount" style="font-size: 12px; color: var(--text-secondary); margin-left: 10px;">
                            (未选择设备)
                        </span>
                    </h3>
                    <div class="parameter-multiselect-container">
                        <div class="parameter-multiselect" id="parameterMultiselect">
                            <span class="parameter-multiselect-placeholder">请选择参数</span>
                        </div>
                        <div class="parameter-dropdown" id="parameterDropdown">
                            <!-- 参数选项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 时间筛选 -->
                <div class="time-filter-section">
                    <h3 class="section-title">
                        <i class="fas fa-clock"></i>
                        时间筛选
                    </h3>
                    <div class="time-mode-selector">
                        <button class="time-mode-btn active" id="realtimeMode">
                            实时模式
                        </button>
                        <button class="time-mode-btn" id="historyMode">
                            历史模式
                        </button>
                    </div>

                    <!-- 历史模式时间选择控件 -->
                    <div class="history-time-controls" id="historyTimeControls" style="display: none;">
                        <!-- 快捷时间按钮 -->
                        <div class="time-shortcuts">
                            <div class="shortcuts-label">快捷选择：</div>
                            <div class="shortcuts-buttons">
                                <button class="shortcut-btn active" data-hours="1">最近1小时</button>
                                <button class="shortcut-btn" data-hours="6">最近6小时</button>
                                <button class="shortcut-btn" data-hours="24">最近24小时</button>
                                <button class="shortcut-btn" data-hours="168">最近7天</button>
                            </div>
                        </div>

                        <!-- 自定义时间选择 -->
                        <div class="custom-time-selection">
                            <div class="time-input-group">
                                <label for="startTime">开始时间：</label>
                                <input type="datetime-local" id="startTime" class="time-input">
                            </div>
                            <div class="time-input-group">
                                <label for="endTime">结束时间：</label>
                                <input type="datetime-local" id="endTime" class="time-input">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 查询数据按钮 -->
                <div class="query-section" style="margin-top: 25px; text-align: center;">
                    <button class="apply-time-btn" id="applyTimeRange" style="margin: 0 auto;">
                        <i class="fas fa-search"></i> 查询数据
                    </button>
                </div>
            </aside>

            <!-- 右侧图表区域 -->
            <section class="parameter-chart-area">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        参数监控曲线
                    </h3>
                    <div class="chart-controls">
                        <button class="chart-control-btn" onclick="exportDataToExcel()" title="导出数据到Excel">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                        <button class="chart-control-btn" onclick="exportChartImage()" title="导出图表为图片">
                            <i class="fas fa-image"></i> 导出图片
                        </button>
                        <button class="chart-control-btn" onclick="clearChart()">
                            <i class="fas fa-redo"></i> 重置
                        </button>
                    </div>
                </div>

                <div class="chart-container" id="parameterChart">
                    <!-- ECharts图表将在这里渲染 -->
                </div>
            </section>
        </main>
    </div>

    <script>
        /**
         * 参数曲线页面脚本 - API集成版本
         * 实现三个相互关联的API接口：设备列表、参数模型、历史数据
         */

        // API配置 - 与main.html保持一致的认证方式
        const API_CONFIG = {
            baseUrl: 'https://exdraw.qizhiyun.cc/prod-api',
            headers: {
                  'Accept': 'application/json',
                  ...getAuthHeader()
              }
        };

        // 全局变量
        let parameterChart = null;
        let currentTimeMode = 'realtime'; // 'realtime' 或 'history'

        // 数据存储
        let deviceList = []; // 设备列表（曲线类型）
        let currentDevice = null; // 当前选中的设备
        let parameterModels = []; // 当前设备的参数模型列表
        let enabledParameters = new Set(); // 启用的参数标识符
        let historicalData = []; // 历史数据

        // 时间控件相关变量
        let selectedTimeRange = {
            type: 'shortcut', // 'shortcut' 或 'custom'
            hours: 1, // 快捷选择的小时数（默认1小时）
            startTime: null, // 自定义开始时间
            endTime: null // 自定义结束时间
        };

        // 加载状态管理
        let loadingStates = {
            devices: false,
            parameters: false,
            history: false
        };

        // 预定义颜色列表，用于参数曲线
        const PARAMETER_COLORS = [
            '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3',
            '#54a0ff', '#5f27cd', '#00d2d3', '#ff6348', '#2ed573', '#ffa502',
            '#a55eea', '#26de81', '#fd79a8', '#fdcb6e', '#6c5ce7', '#74b9ff'
        ];

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initParameterCurvePage();
        });

        // 添加调试函数
        window.debugAPI = function() {
            console.log('=== API调试信息 ===');
            console.log('API配置:', API_CONFIG);
            console.log('当前设备:', currentDevice);
            console.log('设备列表:', deviceList);
            console.log('参数模型:', parameterModels);
            console.log('启用参数:', Array.from(enabledParameters));
            console.log('加载状态:', loadingStates);

            // 测试API连接
            testAPIConnection();
        };

        async function testAPIConnection() {
            console.log('测试API连接...');
            try {
                const testUrl = `${API_CONFIG.baseUrl}/iot/device/shortList?pageNum=1&pageSize=1&groupId=8`;
                const response = await fetch(testUrl, {
                    method: 'GET',
                    headers: API_CONFIG.headers,
                    mode: 'cors',
                    credentials: 'omit'
                });

                console.log('API连接测试结果:', {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries())
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('API响应数据:', data);
                } else {
                    console.error('API响应错误:', response.status, response.statusText);
                }
            } catch (error) {
                console.error('API连接失败:', error);
            }
        }

        // ==================== API调用函数 ====================

        /**
         * 获取设备列表（曲线类型选择）
         * API: GET /iot/device/shortList?pageNum=1&pageSize=9999&groupId=8
         */
        async function fetchDeviceList() {
            try {
                setLoadingState('devices', true);
                console.log('开始获取设备列表...');

                const url = `${API_CONFIG.baseUrl}/iot/device/shortList?pageNum=1&pageSize=9999&groupId=8`;
                const response = await fetch(url, {
                    method: 'GET',
                    headers: API_CONFIG.headers,
                    mode: 'cors',
                    credentials: 'omit'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('设备列表API响应:', data);

                if (data.code === 200 && data.rows) {
                    deviceList = data.rows;
                    populateDeviceSelect(deviceList);
                    console.log(`成功获取${deviceList.length}个设备`);
                } else {
                    throw new Error(data.msg || '获取设备列表失败');
                }

            } catch (error) {
                console.error('获取设备列表失败:', error);

                // 更新设备选择框显示错误信息
                const deviceSelect = document.getElementById('curveTypeSelect');
                if (deviceSelect) {
                    if (error.message.includes('CORS')) {
                        deviceSelect.innerHTML = '<option>网络错误：CORS问题</option>';
                    } else if (error.message.includes('401') || error.message.includes('403')) {
                        deviceSelect.innerHTML = '<option>认证失败：Token无效</option>';
                    } else {
                        deviceSelect.innerHTML = '<option>获取设备列表失败</option>';
                    }
                    deviceSelect.disabled = true;
                }

                showError('获取设备列表失败: ' + error.message);
            } finally {
                setLoadingState('devices', false);
            }
        }

        /**
         * 获取设备参数模型（参数控制列表）
         * API: GET /iot/device/listThingsModel?deviceId={deviceId}&pageNum=1&pageSize=9999
         */
        async function fetchParameterModels(deviceId) {
            try {
                setLoadingState('parameters', true);
                console.log('=== 获取参数模型 ===');
                console.log('设备ID:', deviceId);
                console.log('当前设备:', currentDevice);

                const url = `${API_CONFIG.baseUrl}/iot/device/listThingsModel?deviceId=${deviceId}&pageNum=1&pageSize=9999`;
                console.log('请求URL:', url);
                console.log('请求头:', API_CONFIG.headers);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: API_CONFIG.headers,
                    mode: 'cors',
                    credentials: 'omit'
                });

                console.log('响应状态:', response.status, response.statusText);
                console.log('响应头:', Object.fromEntries(response.headers.entries()));

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('参数模型API响应:', data);
                console.log('响应数据类型:', typeof data);
                console.log('data.code:', data.code);
                console.log('data.rows:', data.rows);
                console.log('data.rows长度:', data.rows ? data.rows.length : 'undefined');

                if (data.code === 200 && data.rows) {
                    parameterModels = data.rows;
                    populateParameterControls(parameterModels);
                    console.log(`成功获取${parameterModels.length}个参数模型`);

                    // 默认开启前3个参数
                    enableDefaultParameters();

                    // 如果有启用的参数，立即获取历史数据
                    if (enabledParameters.size > 0) {
                        await fetchHistoricalData();
                    }
                } else {
                    throw new Error(data.msg || '获取参数模型失败');
                }

            } catch (error) {
                console.error('获取参数模型失败:', error);

                // 清空参数列表并显示错误信息
                const container = document.getElementById('parameterControlList');
                if (container) {
                    if (error.message.includes('CORS')) {
                        container.innerHTML = '<div class="no-parameters">网络错误：CORS问题，请检查服务器配置</div>';
                    } else if (error.message.includes('401') || error.message.includes('403')) {
                        container.innerHTML = '<div class="no-parameters">认证失败：请检查Token是否有效</div>';
                    } else {
                        container.innerHTML = `<div class="no-parameters">获取参数失败：${error.message}</div>`;
                    }
                }

                showError('获取参数模型失败: ' + error.message);
            } finally {
                setLoadingState('parameters', false);
            }
        }

        /**
         * 获取历史数据（图表绘制）
         * API: POST /data/center/deviceHistory
         */
        async function fetchHistoricalData() {
            if (!currentDevice || enabledParameters.size === 0) {
                console.log('没有选中设备或启用参数，跳过历史数据获取');
                return;
            }

            try {
                setLoadingState('history', true);

                // 更新应用按钮状态
                const applyButton = document.getElementById('applyTimeRange');
                if (applyButton) {
                    applyButton.disabled = true;
                    applyButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 查询中...';
                }

                console.log('开始获取历史数据...');

                // 构建请求参数
                const requestBody = buildHistoryRequestBody();
                console.log('历史数据请求参数:', requestBody);

                const url = `${API_CONFIG.baseUrl}/data/center/deviceHistory`;
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        ...API_CONFIG.headers,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody),
                    mode: 'cors',
                    credentials: 'omit'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('历史数据API响应:', data);

                if (data.code === 200 && data.data) {
                    historicalData = data.data;
                    processHistoricalData(historicalData);
                    updateChart();
                    console.log(`成功获取${historicalData.length}个时间点的历史数据`);
                } else {
                    throw new Error(data.msg || '获取历史数据失败');
                }

            } catch (error) {
                console.error('获取历史数据失败:', error);
                showError('获取历史数据失败: ' + error.message);
            } finally {
                setLoadingState('history', false);

                // 恢复应用按钮状态
                const applyButton = document.getElementById('applyTimeRange');
                if (applyButton) {
                    applyButton.disabled = false;
                    applyButton.innerHTML = '<i class="fas fa-search"></i> 查询数据';
                }
            }
        }

        /**
         * 初始化参数曲线页面
         */
        function initParameterCurvePage() {
            console.log('=== 初始化参数曲线页面 ===');
            console.log('API配置:', API_CONFIG);

            // 初始化图表
            initParameterChart();

            // 初始化时间模式切换
            initializeTimeModeToggle();

            // 初始化时间控件
            initializeTimeControls();

            // 初始化设备选择事件
            initializeDeviceSelectEvents();

            // 获取设备列表
            fetchDeviceList();

            console.log('参数曲线页面初始化完成');
        }

        // ==================== 辅助函数 ====================

        /**
         * 构建历史数据请求参数
         */
        function buildHistoryRequestBody() {
            // 构建标识符列表
            const identifierList = Array.from(enabledParameters).map(identifier => {
                const parameter = parameterModels.find(p => p.identifier === identifier);
                return {
                    identifier: identifier,
                    type: parameter ? parameter.type : 1
                };
            });

            // 根据用户选择计算时间范围
            let beginTime, endTime;

            if (currentTimeMode === 'realtime' || selectedTimeRange.type === 'shortcut') {
                // 实时模式或快捷选择：使用小时数计算
                endTime = new Date();
                const hours = currentTimeMode === 'realtime' ? 1 : selectedTimeRange.hours;
                beginTime = new Date(endTime.getTime() - hours * 60 * 60 * 1000);
            } else {
                // 自定义时间选择
                beginTime = selectedTimeRange.startTime ? new Date(selectedTimeRange.startTime) : new Date(Date.now() - 24 * 60 * 60 * 1000);
                endTime = selectedTimeRange.endTime ? new Date(selectedTimeRange.endTime) : new Date();
            }

            console.log('构建历史数据请求参数:', {
                timeMode: currentTimeMode,
                timeRange: selectedTimeRange,
                beginTime: formatDateTime(beginTime),
                endTime: formatDateTime(endTime)
            });

            return {
                deviceId: currentDevice.deviceId,
                serialNumber: currentDevice.serialNumber,
                identifierList: identifierList,
                beginTime: formatDateTime(beginTime),
                endTime: formatDateTime(endTime)
            };
        }

        /**
         * 格式化日期时间
         */
        function formatDateTime(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }

        /**
         * 设置加载状态
         */
        function setLoadingState(type, isLoading) {
            loadingStates[type] = isLoading;
            updateLoadingIndicators(type);
        }

        /**
         * 更新加载指示器
         */
        function updateLoadingIndicators(type) {
            // 设备选择加载状态
            const deviceSelect = document.getElementById('curveTypeSelect');
            if (deviceSelect) {
                deviceSelect.disabled = loadingStates.devices;
                if (loadingStates.devices) {
                    deviceSelect.innerHTML = '<option>正在加载设备列表...</option>';
                }
            }
            console.log(type);
            console.log(loadingStates);
            if(type === 'parameters'){
            // 参数控制加载状态 - 只在参数加载时更新，避免与历史数据加载冲突
                const parameterList = document.getElementById('parameterControlList');
                if (parameterList && loadingStates.parameters) {
                    // 只有在参数加载状态为true时才显示加载提示
                    parameterList.innerHTML = '<div class="loading-indicator">正在加载参数列表...</div>';
                }
                // 注意：参数加载完成后的内容由 populateParameterControls 函数处理
                // 历史数据加载不应该影响参数控制列表的显示
            }
            // 图表加载状态 - 独立处理，不影响参数列表、
            if (loadingStates.history) {
                showChartLoading();
            } else {
                hideChartLoading();
            }
        }

        /**
         * 显示错误信息
         */
        function showError(message) {
            console.error('错误:', message);
            // 可以在这里添加用户友好的错误提示UI
            alert('错误: ' + message);
        }

        /**
         * 显示图表加载状态
         */
        function showChartLoading() {
            if (parameterChart) {
                parameterChart.showLoading('default', {
                    text: '正在加载数据...',
                    color: '#4ecdc4',
                    textColor: '#ffffff',
                    maskColor: 'rgba(0, 0, 0, 0.3)'
                });
            }
        }

        /**
         * 隐藏图表加载状态
         */
        function hideChartLoading() {
            if (parameterChart) {
                parameterChart.hideLoading();
            }
        }

        // ==================== UI更新函数 ====================

        /**
         * 填充设备选择下拉框
         */
        function populateDeviceSelect(devices) {
            const select = document.getElementById('curveTypeSelect');
            if (!select) return;

            // 清空现有选项
            select.innerHTML = '<option value="">请选择设备</option>';

            // 添加设备选项
            devices.forEach(device => {
                const option = document.createElement('option');
                option.value = device.deviceId;
                option.textContent = device.deviceName;
                option.dataset.serialNumber = device.serialNumber;
                select.appendChild(option);
            });

            console.log(`已填充${devices.length}个设备到下拉框`);
        }

        /**
         * 填充参数下拉多选框
         */
        function populateParameterControls(parameters) {
            console.log('=== 填充参数下拉多选框 ===');
            console.log('参数数组:', parameters);
            console.log('参数数量:', parameters ? parameters.length : 'undefined');

            const multiselect = document.getElementById('parameterMultiselect');
            const dropdown = document.getElementById('parameterDropdown');
            const parameterCount = document.getElementById('parameterCount');

            if (!multiselect || !dropdown) {
                console.error('找不到参数多选框元素');
                return;
            }

            // 清空现有内容
            dropdown.innerHTML = '';
            multiselect.innerHTML = '<span class="parameter-multiselect-placeholder">请选择参数</span>';

            if (!parameters || parameters.length === 0) {
                multiselect.innerHTML = '<span class="parameter-multiselect-placeholder">该设备暂无可监控参数</span>';
                if (parameterCount && currentDevice) {
                    parameterCount.textContent = `(${currentDevice.deviceName} - 0个参数)`;
                }
                return;
            }

            // 更新参数计数
            if (parameterCount && currentDevice) {
                parameterCount.textContent = `(${currentDevice.deviceName} - ${parameters.length}个参数)`;
            }

            // 创建下拉选项
            parameters.forEach((parameter, index) => {
                const color = PARAMETER_COLORS[index % PARAMETER_COLORS.length];
                
                const item = document.createElement('div');
                item.className = 'parameter-dropdown-item';
                item.dataset.identifier = parameter.identifier;
                item.dataset.color = color;
                item.dataset.name = parameter.modelName;

                item.innerHTML = `
                    <input type="checkbox" class="parameter-checkbox" id="param-${parameter.identifier}">
                    <div class="parameter-dropdown-color" style="background-color: ${color}"></div>
                    <div class="parameter-dropdown-text" title="${parameter.modelName}">${parameter.modelName}</div>
                    <div class="parameter-dropdown-identifier">${parameter.identifier}</div>
                `;

                // 添加点击事件
                const checkbox = item.querySelector('.parameter-checkbox');
                item.addEventListener('click', (e) => {
                    if (e.target.type !== 'checkbox') {
                        checkbox.checked = !checkbox.checked;
                    }
                    handleParameterSelection(parameter.identifier, checkbox.checked, parameter.modelName, color);
                });

                checkbox.addEventListener('change', (e) => {
                    e.stopPropagation();
                    handleParameterSelection(parameter.identifier, e.target.checked, parameter.modelName, color);
                });

                dropdown.appendChild(item);
            });

            // 初始化多选框事件（只在第一次时初始化）
            if (!window.multiselectEventsInitialized) {
                initializeMultiselectEvents();
                window.multiselectEventsInitialized = true;
                console.log('✅ 多选框事件初始化完成');
            }
            
            console.log(`✅ 成功创建${parameters.length}个参数选项`);
        }

        /**
         * 初始化多选框事件
         */
        function initializeMultiselectEvents() {
            const multiselect = document.getElementById('parameterMultiselect');
            const dropdown = document.getElementById('parameterDropdown');

            // 检查元素是否存在
            if (!multiselect || !dropdown) {
                console.error('无法找到parameter-multiselect或parameter-dropdown元素');
                return;
            }

            // 点击多选框显示/隐藏下拉菜单
            multiselect.addEventListener('click', (e) => {
                console.log('点击parameter-multiselect');
                // 阻止事件冒泡，防止立即触发document的点击事件
                e.stopPropagation();
                
                // 切换下拉菜单显示状态
                dropdown.classList.toggle('show');
                console.log('下拉菜单显示状态:', dropdown.classList.contains('show'));
            });

            // 点击外部关闭下拉菜单
            document.addEventListener('click', (e) => {
                // 检查点击的元素是否在下拉菜单内部
                if (!multiselect.contains(e.target) && !dropdown.contains(e.target)) {
                    // 只有当下拉菜单当前是显示状态时才移除show类
                    if (dropdown.classList.contains('show')) {
                        dropdown.classList.remove('show');
                        console.log('下拉菜单已隐藏');
                    }
                }
            });

            console.log('✅ 多选框事件初始化完成');
        }

        /**
         * 处理参数选择
         */
        function handleParameterSelection(identifier, isSelected, name, color) {
            console.log(`参数选择变更: ${identifier} = ${isSelected}`);
            
            handleParameterSelectionChange(identifier, isSelected);
            updateSelectedParameterTags();
        }

        /**
         * 更新已选参数标签显示
         */
        function updateSelectedParameterTags() {
            const multiselect = document.getElementById('parameterMultiselect');
            const parameters = parameterModels;
            
            if (enabledParameters.size === 0) {
                multiselect.innerHTML = '<span class="parameter-multiselect-placeholder">请选择参数</span>';
                return;
            }

            multiselect.innerHTML = '';
            
            enabledParameters.forEach(identifier => {
                const parameter = parameters.find(p => p.identifier === identifier);
                if (!parameter) return;

                const index = parameters.indexOf(parameter);
                const color = PARAMETER_COLORS[index % PARAMETER_COLORS.length];

                const tag = document.createElement('div');
                tag.className = 'parameter-tag';
                tag.innerHTML = `
                    <span class="parameter-tag-text" title="${parameter.modelName}">${parameter.modelName}</span>
                    <span class="parameter-tag-remove" data-identifier="${identifier}">×</span>
                `;

                // 添加删除标签事件
                tag.querySelector('.parameter-tag-remove').addEventListener('click', (e) => {
                    e.stopPropagation();
                    handleParameterSelection(identifier, false);
                    
                    // 同步更新复选框状态
                    const checkbox = document.querySelector(`#param-${identifier}`);
                    if (checkbox) {
                        checkbox.checked = false;
                    }
                });

                multiselect.appendChild(tag);
            });
        }

        /**
         * 默认开启前3个参数
         */
        function enableDefaultParameters() {
            const parameters = parameterModels;
            if (!parameters || parameters.length === 0) return;

            // 清空已启用参数集合
            enabledParameters.clear();

            // 选择前几个参数作为默认启用
            const defaultCount = Math.min(3, parameters.length);
            for (let i = 0; i < defaultCount; i++) {
                const parameter = parameters[i];
                enabledParameters.add(parameter.identifier);
                console.log(`默认开启参数: ${parameter.identifier}`);
            }

            // 更新复选框状态
            const checkboxes = document.querySelectorAll('.parameter-checkbox');
            checkboxes.forEach(checkbox => {
                const identifier = checkbox.id.replace('param-', '');
                checkbox.checked = enabledParameters.has(identifier);
            });

            // 更新标签显示
            updateSelectedParameterTags();
        }

        /**
         * 初始化设备选择事件
         */
        function initializeDeviceSelectEvents() {
            const select = document.getElementById('curveTypeSelect');
            if (!select) return;

            select.addEventListener('change', async (e) => {
                const deviceId = e.target.value;
                console.log('=== 设备选择变化 ===');
                console.log('选择的设备ID:', deviceId);
                console.log('当前设备列表:', deviceList);

                if (!deviceId) {
                    currentDevice = null;
                    clearParameterControls();
                    clearChart();
                    return;
                }

                // 找到选中的设备
                const selectedDevice = deviceList.find(d => d.deviceId == deviceId);
                console.log('找到的设备:', selectedDevice);

                if (selectedDevice) {
                    currentDevice = selectedDevice;
                    console.log('设置当前设备:', currentDevice);

                    // 清空之前的参数状态
                    enabledParameters.clear();
                    clearChart();

                    // 获取该设备的参数模型
                    console.log('开始获取参数模型...');
                    await fetchParameterModels(deviceId);
                } else {
                    console.error('未找到对应的设备，deviceId:', deviceId);
                }
            });
        }

        /**
         * 处理参数选择变更
         */
        function handleParameterSelectionChange(identifier, isEnabled) {
            if (isEnabled) {
                enabledParameters.add(identifier);
                console.log(`启用参数: ${identifier}`);
            } else {
                enabledParameters.delete(identifier);
                console.log(`禁用参数: ${identifier}`);
            }

            // 重新获取历史数据
            if (currentDevice && enabledParameters.size > 0) {
                fetchHistoricalData();
            } else {
                clearChart();
            }
        }

        /**
         * 清空参数控制列表
         */
        function clearParameterControls() {
            const multiselect = document.getElementById('parameterMultiselect');
            const dropdown = document.getElementById('parameterDropdown');
            const parameterCount = document.getElementById('parameterCount');

            if (multiselect) {
                multiselect.innerHTML = '<span class="parameter-multiselect-placeholder">请先选择设备</span>';
            }
            if (dropdown) {
                dropdown.innerHTML = '';
            }
            if (parameterCount) {
                parameterCount.textContent = '';
            }
        }

        /**
         * 清空图表
         */
        function clearChart() {
            if (parameterChart) {
                parameterChart.clear();
            }
        }

        // ==================== 数据处理函数 ====================

        /**
         * 处理历史数据
         */
        function processHistoricalData(data) {
            if (!data || data.length === 0) {
                console.log('没有历史数据');
                return;
            }

            console.log('开始处理历史数据，数据点数量:', data.length);

            // 重置图表数据
            const chartData = {
                times: [],
                series: {}
            };

            // 初始化每个启用参数的数据系列
            enabledParameters.forEach(identifier => {
                const parameter = parameterModels.find(p => p.identifier === identifier);
                if (parameter) {
                    chartData.series[identifier] = {
                        name: truncateText(parameter.modelName, 12), // 截断显示名称
                        fullName: parameter.modelName, // 保存完整名称用于tooltip
                        data: [],
                        color: getParameterColor(identifier)
                    };
                }
            });

            // 处理每个时间点的数据
            data.forEach(timePoint => {
                // 获取时间戳（对象的第一个键）
                const timestamp = Object.keys(timePoint)[0];
                const values = timePoint[timestamp];

                chartData.times.push(timestamp);

                // 处理每个参数的值
                enabledParameters.forEach(identifier => {
                    if (chartData.series[identifier]) {
                        // 在values数组中查找对应的参数值
                        let value = null;
                        if (values && Array.isArray(values)) {
                            const valueObj = values.find(v => v.hasOwnProperty(identifier));
                            if (valueObj) {
                                value = valueObj[identifier];
                            }
                        }

                        // 处理null值，转换为0或保持null
                        const processedValue = value !== null ? parseFloat(value) || 0 : null;
                        chartData.series[identifier].data.push(processedValue);
                    }
                });
            });

            // 存储处理后的数据
            window.processedChartData = chartData;
            console.log('历史数据处理完成:', chartData);
        }

        /**
         * 获取参数颜色
         */
        function getParameterColor(identifier) {
            const index = Array.from(enabledParameters).indexOf(identifier);
            return PARAMETER_COLORS[index % PARAMETER_COLORS.length];
        }

        /**
         * 截断长文本并添加省略号
         * @param {string} text - 原始文本
         * @param {number} maxLength - 最大长度
         * @returns {string} - 截断后的文本
         */
        function truncateText(text, maxLength = 15) {
            if (!text) return '';
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }

        /**
         * 生成图表标题（包含时间范围信息）
         */
        function generateChartTitle() {
            let title = currentDevice ? `${currentDevice.deviceName} - 参数监控曲线` : '参数监控曲线';

            if (currentTimeMode === 'history') {
                let timeRangeText = '';

                if (selectedTimeRange.type === 'shortcut') {
                    if (selectedTimeRange.hours < 24) {
                        timeRangeText = `（最近${selectedTimeRange.hours}小时）`;
                    } else {
                        const days = selectedTimeRange.hours / 24;
                        timeRangeText = `（最近${days}天）`;
                    }
                } else {
                    // 自定义时间范围
                    const startTime = selectedTimeRange.startTime ? new Date(selectedTimeRange.startTime) : null;
                    const endTime = selectedTimeRange.endTime ? new Date(selectedTimeRange.endTime) : null;

                    if (startTime && endTime) {
                        const formatDate = (date) => {
                            const month = String(date.getMonth() + 1).padStart(2, '0');
                            const day = String(date.getDate()).padStart(2, '0');
                            const hours = String(date.getHours()).padStart(2, '0');
                            const minutes = String(date.getMinutes()).padStart(2, '0');
                            return `${month}-${day} ${hours}:${minutes}`;
                        };

                        timeRangeText = `（${formatDate(startTime)} ~ ${formatDate(endTime)}）`;
                    }
                }

                title += timeRangeText;
            }

            return title;
        }

        /**
         * 更新图表
         */
        function updateChart() {
            if (!parameterChart || !window.processedChartData) {
                console.log('图表或数据未准备好');
                return;
            }

            const chartData = window.processedChartData;

            // 构建ECharts配置
            const option = {
                title: {
                    text: generateChartTitle(),
                    left: 'center',
                    textStyle: {
                        color: '#ffffff',
                        fontSize: 16
                    }
                },
                toolbox: {
                    show: true,
                    orient: 'vertical',
                    right: 20,
                    top: 60,
                    feature: {
                        dataView: {
                            show: true,
                            title: '数据视图',
                            lang: ['数据视图', '关闭', '刷新'],
                            backgroundColor: 'rgba(26, 31, 46, 0.95)',
                            textareaColor: '#ffffff',
                            textareaBorderColor: 'rgba(0, 212, 255, 0.3)',
                            textColor: '#ffffff',
                            buttonColor: 'rgba(0, 212, 255, 0.8)',
                            buttonTextColor: '#ffffff',
                            readOnly: true,
                            optionToContent: function(opt) {
                                return generateDataViewContent(opt);
                            }
                        },
                        saveAsImage: {
                            show: true,
                            title: '保存为图片',
                            type: 'png',
                            backgroundColor: 'rgba(26, 31, 46, 0.95)',
                            name: generateExportFileName('chart'),
                            pixelRatio: 2
                        },
                        myExportData: {
                            show: true,
                            title: '导出数据',
                            icon: 'path://M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z M623.6 316.7C593.6 297.9 512 268 512 268s-81.6 29.9-111.6 48.7c-30 18.8-30 50.4 0 69.2 30 18.8 111.6 48.7 111.6 48.7s81.6-29.9 111.6-48.7c30-18.8 30-50.4 0-69.2z M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326z M633.6 776H390.4c-4.4 0-8-3.6-8-8v-56c0-4.4 3.6-8 8-8h243.2c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8z M633.6 616H390.4c-4.4 0-8-3.6-8-8v-56c0-4.4 3.6-8 8-8h243.2c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8z M633.6 456H390.4c-4.4 0-8-3.6-8-8v-56c0-4.4 3.6-8 8-8h243.2c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8z',
                            onclick: function() {
                                exportDataToExcel();
                            }
                        }
                    },
                    iconStyle: {
                        borderColor: 'rgba(0, 212, 255, 0.8)',
                        color: 'rgba(0, 212, 255, 0.8)',
                        backgroundColor: 'rgba(26, 31, 46, 0.8)'
                    },
                    emphasis: {
                        iconStyle: {
                            borderColor: '#ffffff',
                            color: '#ffffff',
                            backgroundColor: 'rgba(0, 212, 255, 0.2)'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(26, 31, 46, 0.95)',
                        borderColor: 'rgba(0, 212, 255, 0.3)',
                        textStyle: {
                            color: '#ffffff'
                        }
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    borderColor: '#4ecdc4',
                    textStyle: {
                        color: '#ffffff'
                    },
                    formatter: function(params) {
                        let result = `<div style="margin-bottom: 5px;">${params[0].axisValue}</div>`;
                        params.forEach(param => {
                            const value = param.value !== null ? param.value : 'N/A';
                            // 在tooltip中显示完整的参数名称
                            const fullName = Object.values(chartData.series).find(s => s.name === param.seriesName)?.fullName || param.seriesName;
                            result += `<div style="color: ${param.color};">● ${fullName}: ${value}</div>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: Object.values(chartData.series).map(s => ({
                        name: s.name,
                        // 为legend项添加完整名称的tooltip
                        tooltip: {
                            show: true,
                            formatter: s.fullName || s.name
                        }
                    })),
                    textStyle: {
                        color: '#ffffff',
                        fontSize: 12
                    },
                    top: 30,
                    // 设置legend的最大宽度，超出时显示省略号
                    itemWidth: 14,
                    itemHeight: 14,
                    formatter: function(name) {
                        return truncateText(name, 12);
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: chartData.times,
                    axisLine: {
                        lineStyle: {
                            color: '#4ecdc4'
                        }
                    },
                    axisLabel: {
                        color: '#ffffff',
                        formatter: function(value) {
                            // 只显示时间部分
                            return value.split(' ')[1] || value;
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#4ecdc4'
                        }
                    },
                    axisLabel: {
                        color: '#ffffff'
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(78, 205, 196, 0.2)'
                        }
                    }
                },
                series: Object.values(chartData.series).map(seriesData => ({
                    name: seriesData.name,
                    type: 'line',
                    data: seriesData.data,
                    smooth: true,
                    lineStyle: {
                        color: seriesData.color,
                        width: 2
                    },
                    itemStyle: {
                        color: seriesData.color
                    },
                    connectNulls: false // 不连接null值
                }))
            };

            parameterChart.setOption(option, true);
            console.log('图表更新完成');
        }

        /**
         * 初始化参数图表
         */
        function initParameterChart() {
            const chartContainer = document.getElementById('parameterChart');
            if (!chartContainer) {
                console.error('找不到图表容器元素');
                return;
            }

            // 初始化ECharts实例
            parameterChart = echarts.init(chartContainer);

            // 设置初始的空图表配置
            const initialOption = {
                backgroundColor: 'transparent',
                title: {
                    text: '参数监控曲线',
                    left: 'center',
                    textStyle: {
                        color: '#ffffff',
                        fontSize: 16
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: [],
                    axisLine: {
                        lineStyle: {
                            color: '#4ecdc4'
                        }
                    },
                    axisLabel: {
                        color: '#ffffff'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#4ecdc4'
                        }
                    },
                    axisLabel: {
                        color: '#ffffff'
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(78, 205, 196, 0.2)'
                        }
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    borderColor: '#4ecdc4',
                    textStyle: {
                        color: '#ffffff'
                    }
                },
                legend: {
                    textStyle: {
                        color: '#ffffff'
                    },
                    top: 30
                },
                series: []
            };

            parameterChart.setOption(initialOption);

            // 响应式调整
            window.addEventListener('resize', () => {
                if (parameterChart) {
                    parameterChart.resize();
                }
            });

            console.log('参数图表初始化完成');
        }

        /**
         * 初始化时间模式切换
         */
        function initializeTimeModeToggle() {
            const realtimeBtn = document.getElementById('realtimeMode');
            const historyBtn = document.getElementById('historyMode');

            if (realtimeBtn) {
                realtimeBtn.addEventListener('click', () => {
                    switchTimeMode('realtime');
                });
            }

            if (historyBtn) {
                historyBtn.addEventListener('click', () => {
                    switchTimeMode('history');
                });
            }

            // 默认设置为实时模式
            switchTimeMode('realtime');
        }

        /**
         * 切换时间模式
         */
        function switchTimeMode(mode) {
            currentTimeMode = mode;

            // 更新按钮状态
            const realtimeBtn = document.getElementById('realtimeMode');
            const historyBtn = document.getElementById('historyMode');
            const historyControls = document.getElementById('historyTimeControls');

            if (realtimeBtn && historyBtn) {
                realtimeBtn.classList.toggle('active', mode === 'realtime');
                historyBtn.classList.toggle('active', mode === 'history');
            }

            // 显示/隐藏历史时间控件
            if (historyControls) {
                historyControls.style.display = mode === 'history' ? 'block' : 'none';
            }

            console.log('切换时间模式:', mode);

            // 如果有当前设备和启用的参数，重新获取数据
            if (currentDevice && enabledParameters.size > 0) {
                fetchHistoricalData();
            }
        }

        /**
         * 初始化时间控件
         */
        function initializeTimeControls() {
            // 设置默认时间值
            setDefaultTimeValues();

            // 初始化快捷时间按钮事件
            initializeShortcutButtons();

            // 初始化自定义时间输入事件
            initializeCustomTimeInputs();

            // 初始化应用按钮事件
            initializeApplyButton();

            console.log('时间控件初始化完成');
        }

        /**
         * 设置默认时间值
         */
        function setDefaultTimeValues() {
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 1 * 60 * 60 * 1000);

            const startTimeInput = document.getElementById('startTime');
            const endTimeInput = document.getElementById('endTime');

            if (startTimeInput && endTimeInput) {
                startTimeInput.value = formatDateTimeForInput(oneHourAgo);
                endTimeInput.value = formatDateTimeForInput(now);

                // 更新选择的时间范围
                selectedTimeRange.startTime = startTimeInput.value;
                selectedTimeRange.endTime = endTimeInput.value;
            }
        }

        /**
         * 格式化日期时间为input控件格式
         */
        function formatDateTimeForInput(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');

            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        /**
         * 初始化快捷时间按钮事件
         */
        function initializeShortcutButtons() {
            const shortcutButtons = document.querySelectorAll('.shortcut-btn');

            shortcutButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    const hours = parseInt(e.target.dataset.hours);

                    // 更新按钮状态
                    shortcutButtons.forEach(btn => btn.classList.remove('active'));
                    e.target.classList.add('active');

                    // 更新选择的时间范围
                    selectedTimeRange.type = 'shortcut';
                    selectedTimeRange.hours = hours;

                    // 更新自定义时间输入框
                    const now = new Date();
                    const startTime = new Date(now.getTime() - hours * 60 * 60 * 1000);

                    const startTimeInput = document.getElementById('startTime');
                    const endTimeInput = document.getElementById('endTime');

                    if (startTimeInput && endTimeInput) {
                        startTimeInput.value = formatDateTimeForInput(startTime);
                        endTimeInput.value = formatDateTimeForInput(now);
                    }

                    console.log(`选择快捷时间: 最近${hours}小时`);
                });
            });
        }

        /**
         * 初始化自定义时间输入事件
         */
        function initializeCustomTimeInputs() {
            const startTimeInput = document.getElementById('startTime');
            const endTimeInput = document.getElementById('endTime');

            if (startTimeInput && endTimeInput) {
                [startTimeInput, endTimeInput].forEach(input => {
                    input.addEventListener('change', () => {
                        // 切换到自定义模式
                        selectedTimeRange.type = 'custom';
                        selectedTimeRange.startTime = startTimeInput.value;
                        selectedTimeRange.endTime = endTimeInput.value;

                        // 清除快捷按钮的选中状态
                        document.querySelectorAll('.shortcut-btn').forEach(btn => {
                            btn.classList.remove('active');
                        });

                        console.log('自定义时间选择:', {
                            startTime: selectedTimeRange.startTime,
                            endTime: selectedTimeRange.endTime
                        });
                    });
                });
            }
        }

        /**
         * 初始化应用按钮事件
         */
        function initializeApplyButton() {
            const applyButton = document.getElementById('applyTimeRange');

            if (applyButton) {
                applyButton.addEventListener('click', () => {
                    // 验证时间范围
                    if (!validateTimeRange()) {
                        return;
                    }

                    // 如果有当前设备和启用的参数，获取历史数据
                    if (currentDevice && enabledParameters.size > 0) {
                        fetchHistoricalData();
                    } else {
                        alert('请先选择设备和参数');
                    }
                });
            }
        }

        /**
         * 验证时间范围
         */
        function validateTimeRange() {
            if (selectedTimeRange.type === 'custom') {
                const startTime = new Date(selectedTimeRange.startTime);
                const endTime = new Date(selectedTimeRange.endTime);

                if (startTime >= endTime) {
                    alert('开始时间不能晚于或等于结束时间');
                    return false;
                }

                // 检查时间范围是否过大（最多30天）
                const diffDays = (endTime - startTime) / (1000 * 60 * 60 * 24);
                if (diffDays > 30) {
                    alert('时间范围不能超过30天');
                    return false;
                }

                // 检查是否选择了未来时间
                const now = new Date();
                if (endTime > now) {
                    alert('结束时间不能晚于当前时间');
                    return false;
                }
            }

            return true;
        }

        /**
         * 生成导出文件名
         * @param {string} type - 导出类型 ('chart' 或 'data')
         * @returns {string} - 格式化的文件名
         */
        function generateExportFileName(type) {
            const now = new Date();
            const dateStr = formatDateTime(now).replace(/[: ]/g, '-');
            const deviceName = currentDevice ? currentDevice.deviceName : '未知设备';

            if (type === 'chart') {
                return `参数曲线_${deviceName}_${dateStr}`;
            } else {
                return `参数数据_${deviceName}_${dateStr}`;
            }
        }

        /**
         * 生成数据视图内容
         * @param {Object} option - ECharts配置项
         * @returns {string} - 格式化的HTML内容
         */
        function generateDataViewContent(option) {
            if (!window.processedChartData) {
                return '<div style="text-align:center;padding:20px;">暂无数据</div>';
            }

            const chartData = window.processedChartData;
            const seriesNames = Object.values(chartData.series).map(s => s.fullName || s.name);

            let html = `
                <div style="margin:10px 0;font-weight:bold;font-size:14px;">${generateChartTitle()}</div>
                <table style="width:100%;border-collapse:collapse;font-size:12px;">
                    <thead>
                        <tr>
                            <th style="border:1px solid #ccc;padding:5px;text-align:center;background:rgba(0,212,255,0.2);">时间</th>
            `;

            // 添加表头
            seriesNames.forEach(name => {
                html += `<th style="border:1px solid #ccc;padding:5px;text-align:center;background:rgba(0,212,255,0.2);">${name}</th>`;
            });

            html += '</tr></thead><tbody>';

            // 添加数据行
            const times = chartData.times;
            const seriesData = Object.values(chartData.series);

            for (let i = 0; i < times.length; i++) {
                html += `<tr><td style="border:1px solid #ccc;padding:5px;text-align:center;">${times[i]}</td>`;

                seriesData.forEach(series => {
                    const value = series.data[i] !== null ? series.data[i] : 'N/A';
                    html += `<td style="border:1px solid #ccc;padding:5px;text-align:right;">${value}</td>`;
                });

                html += '</tr>';
            }

            html += '</tbody></table>';

            // 添加导出按钮
            html += `
                <div style="margin-top:15px;text-align:center;">
                    <button onclick="exportDataToExcel()" style="padding:5px 15px;background:rgba(0,212,255,0.8);color:#fff;border:none;border-radius:4px;cursor:pointer;">
                        导出到Excel
                    </button>
                </div>
            `;

            return html;
        }

        /**
         * 导出数据到Excel
         */
        function exportDataToExcel() {
            if (!window.processedChartData) {
                alert('暂无数据可导出');
                return;
            }

            try {
                const chartData = window.processedChartData;
                const seriesNames = Object.values(chartData.series).map(s => s.fullName || s.name);
                const times = chartData.times;
                const seriesData = Object.values(chartData.series);

                // 创建工作表数据
                const worksheet = [];

                // 添加表头
                const header = ['时间', ...seriesNames];
                worksheet.push(header);

                // 添加数据行
                for (let i = 0; i < times.length; i++) {
                    const row = [times[i]];

                    seriesData.forEach(series => {
                        row.push(series.data[i] !== null ? series.data[i] : '');
                    });

                    worksheet.push(row);
                }

                // 创建工作簿
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.aoa_to_sheet(worksheet);

                // 设置列宽
                const wscols = [
                    {wch: 20}, // 时间列宽
                    ...seriesNames.map(() => ({wch: 15})) // 数据列宽
                ];
                ws['!cols'] = wscols;

                // 添加工作表到工作簿
                XLSX.utils.book_append_sheet(wb, ws, '参数数据');

                // 导出文件
                XLSX.writeFile(wb, generateExportFileName('data') + '.xlsx');

                console.log('数据导出成功');
            } catch (error) {
                console.error('导出数据失败:', error);
                alert('导出数据失败: ' + error.message);
            }
        }

        /**
         * 导出图表为图片
         */
        function exportChartImage() {
            if (!parameterChart) {
                alert('图表未初始化');
                return;
            }

            try {
                // 获取图表的base64图片数据
                const imageDataURL = parameterChart.getDataURL({
                    type: 'png',
                    pixelRatio: 2,
                    backgroundColor: 'rgba(26, 31, 46, 0.95)'
                });

                // 创建下载链接
                const link = document.createElement('a');
                link.download = generateExportFileName('chart') + '.png';
                link.href = imageDataURL;

                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                console.log('图表导出成功');
            } catch (error) {
                console.error('导出图表失败:', error);
                alert('导出图表失败: ' + error.message);
            }
        }

        /**
         * 清空图表
         */
        function clearChart() {
            if (parameterChart) {
                parameterChart.clear();
                console.log('图表已清空');
            }
        }
    </script>
</body>
</html>
